package com.tool.converge.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 11:28 2025/8/8
 * @Description TODO
 * 预警消息发送方式
 * 1-钉钉，2-短信，3-钉钉+短信
 * @MethodName
 * @return null
 */
@Getter
@AllArgsConstructor
public enum AlertMessageWarnTypeEnum {

    /**
     * 钉钉
     */
    DING_TALK(1,"钉钉"),

    /**
     * 短信
     */
    SMS(2,"短信"),

    /**
     * 钉钉+短信
     */
    DING_TALK_AND_SMS(3,"钉钉+短信");

    private final int code;
    private final String name;
}
