<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>alert-converge</artifactId>
        <groupId>com.tool.converge</groupId>
        <version>1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>alert-converge-common-biz</artifactId>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.tool.converge</groupId>
            <artifactId>alert-converge-env</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.sso</groupId>
            <artifactId>sso-client-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tool.converge</groupId>
            <artifactId>alert-converge-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tool.converge</groupId>
            <artifactId>alert-converge-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-tool</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzed.structure</groupId>
            <artifactId>structure-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>

        <!--prometheus监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
    </dependencies>

</project>