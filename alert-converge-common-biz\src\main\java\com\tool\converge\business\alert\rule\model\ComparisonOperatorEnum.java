package com.tool.converge.business.alert.rule.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 比较运算符枚举
 * 用于预警规则条件判断中的各种比较操作
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Getter
@AllArgsConstructor
public enum ComparisonOperatorEnum {

    /**
     * 大于
     */
    GT("GT", "大于") {
        @Override
        public boolean compare(String value1, String value2) {
            if (StringUtils.isBlank(value1) || StringUtils.isBlank(value2)) {
                return false;
            }
            try {
                BigDecimal num1 = new BigDecimal(value1.trim());
                BigDecimal num2 = new BigDecimal(value2.trim());
                return num1.compareTo(num2) > 0;
            } catch (NumberFormatException e) {
                // 如果不能转换为数字，则进行字符串比较
                return value1.trim().compareTo(value2.trim()) > 0;
            }
        }
    },

    /**
     * 大于等于
     */
    GTE("GTE", "大于等于") {
        @Override
        public boolean compare(String value1, String value2) {
            if (StringUtils.isBlank(value1) || StringUtils.isBlank(value2)) {
                return false;
            }
            try {
                BigDecimal num1 = new BigDecimal(value1.trim());
                BigDecimal num2 = new BigDecimal(value2.trim());
                return num1.compareTo(num2) >= 0;
            } catch (NumberFormatException e) {
                // 如果不能转换为数字，则进行字符串比较
                return value1.trim().compareTo(value2.trim()) >= 0;
            }
        }
    },

    /**
     * 等于
     */
    EQ("EQ", "等于") {
        @Override
        public boolean compare(String value1, String value2) {
            if (value1 == null && value2 == null) {
                return true;
            }
            if (value1 == null || value2 == null) {
                return false;
            }
            try {
                BigDecimal num1 = new BigDecimal(value1.trim());
                BigDecimal num2 = new BigDecimal(value2.trim());
                return num1.compareTo(num2) == 0;
            } catch (NumberFormatException e) {
                // 如果不能转换为数字，则进行字符串比较
                return value1.trim().equals(value2.trim());
            }
        }
    },

    /**
     * 小于
     */
    LT("LT", "小于") {
        @Override
        public boolean compare(String value1, String value2) {
            if (StringUtils.isBlank(value1) || StringUtils.isBlank(value2)) {
                return false;
            }
            try {
                BigDecimal num1 = new BigDecimal(value1.trim());
                BigDecimal num2 = new BigDecimal(value2.trim());
                return num1.compareTo(num2) < 0;
            } catch (NumberFormatException e) {
                // 如果不能转换为数字，则进行字符串比较
                return value1.trim().compareTo(value2.trim()) < 0;
            }
        }
    },

    /**
     * 小于等于
     */
    LTE("LTE", "小于等于") {
        @Override
        public boolean compare(String value1, String value2) {
            if (StringUtils.isBlank(value1) || StringUtils.isBlank(value2)) {
                return false;
            }
            try {
                BigDecimal num1 = new BigDecimal(value1.trim());
                BigDecimal num2 = new BigDecimal(value2.trim());
                return num1.compareTo(num2) <= 0;
            } catch (NumberFormatException e) {
                // 如果不能转换为数字，则进行字符串比较
                return value1.trim().compareTo(value2.trim()) <= 0;
            }
        }
    },

    /**
     * 不等于
     */
    NE("NE", "不等于") {
        @Override
        public boolean compare(String value1, String value2) {
            return !EQ.compare(value1, value2);
        }
    };

    private final String code;
    private final String name;

    /**
     * 执行比较操作
     * 
     * @param value1 值1
     * @param value2 值2
     * @return 比较结果
     */
    public abstract boolean compare(String value1, String value2);

    /**
     * 根据代码获取枚举
     * 
     * @param code 运算符代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static ComparisonOperatorEnum fromCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ComparisonOperatorEnum operator : values()) {
            if (operator.getCode().equals(code.trim().toUpperCase())) {
                return operator;
            }
        }
        return null;
    }
}
