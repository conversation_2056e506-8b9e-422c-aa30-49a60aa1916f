package com.tool.converge.business.alert.rule;

import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;

import java.util.List;

/**
 * 规则引擎服务接口
 * 用于评估预警事件是否匹配配置的规则
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface RuleEngineService {

    /**
     * 评估预警事件是否匹配指定的规则
     *
     * <p>该方法会：</p>
     * <ul>
     *   <li>解析预警事件的payload数据</li>
     *   <li>对指定的规则进行资方匹配验证</li>
     *   <li>对匹配的规则进行条件评估</li>
     *   <li>返回综合的评估结果</li>
     * </ul>
     *
     * <p>处理流程：</p>
     * <ol>
     *   <li>验证预警事件数据的完整性</li>
     *   <li>解析payload JSON数据为键值对映射</li>
     *   <li>根据传入的规则ID列表查询规则详细信息</li>
     *   <li>逐个评估规则：
     *     <ul>
     *       <li>验证资方是否在规则适用范围内</li>
     *       <li>如果资方匹配，则评估规则的所有条件</li>
     *       <li>根据规则匹配模式（满足所有条件/满足任一条件）判断是否匹配</li>
     *     </ul>
     *   </li>
     *   <li>构建并返回评估结果</li>
     * </ol>
     *
     * @param alertEventDO 预警事件，包含事件ID、payload等信息
     * @param ruleIds 需要评估的规则ID列表
     * @return 规则评估结果，包含是否匹配、匹配的规则信息、处理详情等
     *
     * @throws IllegalArgumentException 当预警事件为null或数据不完整时
     * @throws RuntimeException 当发生系统异常时（如数据库连接异常、JSON解析异常等）
     *
     * @see RuleEvaluationResult
     * @see AlertEventDO
     */
    RuleEvaluationResult evaluateRules(AlertEventDO alertEventDO, List<Long> ruleIds);


    /**
     * 检查预警事件数据的完整性
     * 
     * <p>验证预警事件是否包含规则评估所需的基本信息：</p>
     * <ul>
     *   <li>事件ID不能为null</li>
     *   <li>payload不能为空</li>
     *   <li>其他必要字段的完整性</li>
     * </ul>
     * 
     * @param alertEventDO 预警事件
     * @return 是否有效，true表示数据完整，false表示数据不完整
     */
    boolean isValidAlertEvent(AlertEventDO alertEventDO);

}
