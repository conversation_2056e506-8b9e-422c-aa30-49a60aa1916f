package com.tool.converge.repository.domain.system.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.tool.converge.repository.domain.system.db.DingTalkDeptDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 钉钉部门
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DingTalkDeptDetailVO对象", description = "钉钉部门")
public class DingTalkDeptDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "父部门id")
    private Long parentId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 null已删除")
    private Boolean deleted;

    @Schema(description = "子部门列表")
    private List<DingTalkDeptDetailVO> subList;

    public static DingTalkDeptDetailVO of(DingTalkDeptDO entity) {
        if (entity == null) {
            return null;
        }
        DingTalkDeptDetailVO detailVO = new DingTalkDeptDetailVO();
        BeanUtils.copyProperties(entity, detailVO);
        return detailVO;
    }

}
