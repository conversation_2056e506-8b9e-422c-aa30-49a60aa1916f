# 预警规则判断系统需求文档

## 功能概述

在现有的预警消息发送流程中，实现智能规则判断逻辑，确保只有符合配置规则的预警事件才会被发送，提高预警系统的精准度和可配置性。

## 需求列表

### 1. 事件数据获取和解析功能

**用户故事**: 作为系统开发人员，我希望预警系统能够正确获取和解析上报的事件数据，以便为后续的规则判断提供数据基础。

**验收标准**:
1. **WHEN** AlertMessageServiceImpl.send方法被调用时，**IF** 需要进行规则判断，**THEN** 系统应该从AlertEventDO中获取payload字段的JSON数据
2. **WHEN** 获取payload数据后，**IF** payload为JSON字符串格式，**THEN** 系统应该将其解析为PayloadItem列表
3. **WHEN** 解析PayloadItem后，**IF** 解析成功，**THEN** 系统应该构建code-value映射表便于后续查找
4. **WHEN** 数据解析失败时，**IF** payload格式错误或为空，**THEN** 系统应该记录错误日志并跳过规则判断
5. **WHEN** 构建数据映射后，**IF** 需要查找特定字段，**THEN** 系统应该提供根据code快速查找value的方法

### 2. 规则配置查询功能

**用户故事**: 作为系统开发人员，我希望预警系统能够根据事件信息查询相关的规则配置，以便确定需要执行哪些规则判断。

**验收标准**:
1. **WHEN** 事件数据解析完成后，**IF** fullInfo.ruleConditions包含规则信息，**THEN** 系统应该根据规则ID查询完整的规则配置
2. **WHEN** 查询规则配置时，**IF** 需要获取规则详情，**THEN** 系统应该从t_rules表中查询rule_status、rule_matching、apply_investor等字段
3. **WHEN** 查询规则条件时，**IF** 规则有效，**THEN** 系统应该从t_warn_condition表中查询该规则的所有条件配置
4. **WHEN** 查询资方信息时，**IF** 规则包含apply_investor，**THEN** 系统应该解析逗号分隔的资方ID并查询t_capital表获取资方详情
5. **WHEN** 规则状态检查时，**IF** rule_status为"1"（关闭），**THEN** 系统应该跳过该规则的判断

### 3. 资方匹配验证功能

**用户故事**: 作为系统管理员，我希望预警系统能够验证事件的资方是否在规则配置的适用范围内，以便只对相关资方发送预警消息。

**验收标准**:

1. **WHEN** 预警事件触发时，**IF** 事件包含资方信息，**THEN** 系统应该从payload中提取fund_provider_name字段的值
2. **WHEN** 获取到资方名称后，**IF** 存在相关规则配置，**THEN** 系统应该根据资方名称查询t_capital表获取资方ID
3. **WHEN** 获取资方ID后，**IF** 需要匹配规则，**THEN** 系统应该检查t_rules表中apply_investor字段是否包含该资方ID
4. **WHEN** 资方匹配检查完成后，**IF** 事件资方不在任何规则的适用范围内，**THEN** 系统应该跳过预警消息发送并记录日志
5. **WHEN** 资方匹配检查完成后，**IF** 事件资方在规则适用范围内，**THEN** 系统应该继续进行条件规则判断

### 4. 运算符枚举管理功能

**用户故事**: 作为开发人员，我希望系统提供标准化的运算符枚举类，以便统一管理和使用各种比较运算符。

**验收标准**:

1. **WHEN** 系统初始化时，**IF** 需要运算符定义，**THEN** 系统应该提供包含GT、GTE、EQ、LT、LTE、NE的枚举类
2. **WHEN** 进行条件比较时，**IF** 使用运算符，**THEN** 系统应该通过枚举类获取对应的比较逻辑
3. **WHEN** 运算符枚举类定义时，**IF** 需要扩展性，**THEN** 系统应该支持未来添加新的运算符类型
4. **WHEN** 枚举类设计时，**IF** 需要提供比较方法，**THEN** 系统应该为每个运算符提供compare方法实现具体比较逻辑

### 5. 条件规则评估功能

**用户故事**: 作为系统管理员，我希望预警系统能够根据配置的条件规则智能判断事件数据，以便实现灵活的预警触发逻辑。

**验收标准**:

1. **WHEN** 资方匹配成功后，**IF** 存在相关规则条件，**THEN** 系统应该从t_warn_condition表中查询对应的条件配置
2. **WHEN** 获取条件配置后，**IF** 条件包含setting_item、operator、assignment_item，**THEN** 系统应该从事件payload中查找对应的code-value对进行比较
3. **WHEN** 进行条件比较时，**IF** operator为GT，**THEN** 系统应该判断payload中的value是否大于assignment_item
4. **WHEN** 进行条件比较时，**IF** operator为GTE，**THEN** 系统应该判断payload中的value是否大于等于assignment_item
5. **WHEN** 进行条件比较时，**IF** operator为EQ，**THEN** 系统应该判断payload中的value是否等于assignment_item
6. **WHEN** 进行条件比较时，**IF** operator为LT，**THEN** 系统应该判断payload中的value是否小于assignment_item
7. **WHEN** 进行条件比较时，**IF** operator为LTE，**THEN** 系统应该判断payload中的value是否小于等于assignment_item
8. **WHEN** 进行条件比较时，**IF** operator为NE，**THEN** 系统应该判断payload中的value是否不等于assignment_item

### 6. 规则匹配模式支持功能

**用户故事**: 作为系统管理员，我希望预警系统能够支持不同的规则匹配模式，以便灵活配置"满足所有条件"或"满足任一条件"的触发逻辑。

**验收标准**:

1. **WHEN** 进行规则判断时，**IF** t_rules表中rule_matching字段为"1"，**THEN** 系统应该要求满足该规则下的所有条件才触发预警
2. **WHEN** 进行规则判断时，**IF** t_rules表中rule_matching字段为"0"，**THEN** 系统应该只要满足该规则下的任一条件就触发预警
3. **WHEN** 规则包含多个条件时，**IF** 匹配模式为"满足所有条件"，**THEN** 系统应该逐一检查所有条件，只有全部满足才返回匹配成功
4. **WHEN** 规则包含多个条件时，**IF** 匹配模式为"满足任一条件"，**THEN** 系统应该检查条件，只要有一个满足就返回匹配成功

### 7. 多规则处理功能

**用户故事**: 作为系统管理员，我希望预警系统能够处理一个事件可能匹配多个规则的情况，以便实现复杂的预警逻辑。

**验收标准**:

1. **WHEN** 事件触发时，**IF** fullInfo.ruleConditions包含多个规则，**THEN** 系统应该逐一检查每个规则
2. **WHEN** 检查多个规则时，**IF** 其中任何一个规则匹配成功，**THEN** 系统应该触发预警消息发送
3. **WHEN** 检查多个规则时，**IF** 所有规则都不匹配，**THEN** 系统应该跳过预警消息发送
4. **WHEN** 规则匹配成功时，**IF** 需要记录匹配的规则信息，**THEN** 系统应该在预警消息中包含匹配的规则ID和名称

### 8. 数据类型转换和验证功能

**用户故事**: 作为系统管理员，我希望预警系统能够正确处理不同数据类型的比较，以便确保规则判断的准确性。

**验收标准**:

1. **WHEN** 进行数值比较时，**IF** payload中的value为字符串，**THEN** 系统应该尝试转换为数值类型进行比较
2. **WHEN** 数据类型转换失败时，**IF** 无法转换为目标类型，**THEN** 系统应该记录错误日志并跳过该条件判断
3. **WHEN** 进行字符串比较时，**IF** 比较的是字符串类型，**THEN** 系统应该使用字符串比较逻辑
4. **WHEN** assignment_item为null时，**IF** 条件配置不完整，**THEN** 系统应该跳过该条件并记录警告日志

### 9. 日志记录和监控功能

**用户故事**: 作为系统管理员，我希望预警规则判断过程有详细的日志记录，以便进行问题排查和系统监控。

**验收标准**:

1. **WHEN** 规则判断开始时，**IF** 需要记录日志，**THEN** 系统应该记录事件ID、规则数量等基本信息
2. **WHEN** 资方匹配失败时，**IF** 事件资方不在适用范围内，**THEN** 系统应该记录详细的匹配失败原因
3. **WHEN** 条件判断过程中，**IF** 发生异常或错误，**THEN** 系统应该记录错误详情和堆栈信息
4. **WHEN** 规则匹配成功时，**IF** 触发预警，**THEN** 系统应该记录匹配成功的规则信息和触发条件

### 10. 异常处理功能

**用户故事**: 作为系统管理员，我希望预警规则判断功能具有健壮的异常处理机制，以便在出现问题时不影响整个预警流程。

**验收标准**:

1. **WHEN** 数据库查询异常时，**IF** 无法获取规则配置，**THEN** 系统应该记录错误并继续执行预警发送（降级处理）
2. **WHEN** 数据解析异常时，**IF** payload数据格式错误，**THEN** 系统应该记录错误并跳过规则判断
3. **WHEN** 规则配置异常时，**IF** 规则数据不完整或格式错误，**THEN** 系统应该跳过该规则并继续处理其他规则
4. **WHEN** 任何异常发生时，**IF** 不应影响主流程，**THEN** 系统应该确保预警消息发送功能的可用性

## 处理流程顺序

基于以上需求，预警规则判断的处理顺序应该是：

1. **事件数据获取和解析** → 获取并解析payload数据
2. **规则配置查询** → 查询相关规则配置信息
3. **资方匹配验证** → 验证事件资方是否在规则适用范围内
4. **运算符枚举管理** → 提供标准化的比较运算符
5. **条件规则评估** → 根据条件配置判断事件数据
6. **规则匹配模式支持** → 支持"满足所有"或"满足任一"模式
7. **多规则处理** → 处理多个规则的匹配情况
8. **数据类型转换和验证** → 确保数据类型正确性
9. **日志记录和监控** → 记录整个判断过程
10. **异常处理** → 处理各种异常情况
