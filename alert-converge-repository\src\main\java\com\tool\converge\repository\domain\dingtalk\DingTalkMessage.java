package com.tool.converge.repository.domain.dingtalk;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 钉钉机器人消息对象
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DingTalkMessage {

    /**
     * 消息通知地址，webhook
     */
    @NotBlank(message = "消息通知地址不能为空")
    private String url;

    /**
     * 消息类型:text文本
     */
    @NotBlank(message = "消息类型不能为空")
    private String msgtype;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String content;

    /**
     * 是否@全部人：false-否，true-是
     */
    private String isAtAll;

    /**
     * 需要@的联系人手机号列表
     */
    private List<String> atMobiles;

    /**
     * 需要@的联系人userId列表
     */
    private List<String> atUserIds;
}
