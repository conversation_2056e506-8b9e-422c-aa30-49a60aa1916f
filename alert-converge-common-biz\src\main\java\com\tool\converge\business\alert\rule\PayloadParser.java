package com.tool.converge.business.alert.rule;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tool.converge.repository.domain.alert.bo.AlertEventReportBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 事件数据解析器
 * 用于解析预警事件的payload数据，将JSON字符串转换为便于查找的Map结构
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class PayloadParser {

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 解析payload JSON字符串为Map<String, String>
     * 
     * @param payloadJson JSON字符串
     * @return code-value映射表，如果解析失败返回空Map
     */
    public Map<String, String> parsePayload(String payloadJson) {
        if (StringUtils.isBlank(payloadJson)) {
            log.warn("Payload JSON字符串为空");
            return Collections.emptyMap();
        }

        try {
            // 尝试解析为PayloadItem列表
            List<AlertEventReportBO.PayloadItem> payloadItems = objectMapper.readValue(
                payloadJson, 
                new TypeReference<List<AlertEventReportBO.PayloadItem>>() {}
            );

            if (payloadItems == null || payloadItems.isEmpty()) {
                log.warn("解析后的PayloadItem列表为空");
                return Collections.emptyMap();
            }

            // 转换为Map结构便于查找
            Map<String, String> payloadMap = new HashMap<>(16);
            for (AlertEventReportBO.PayloadItem item : payloadItems) {
                if (item != null && StringUtils.isNotBlank(item.getCode())) {
                    // 使用code作为key，value作为值
                    payloadMap.put(item.getCode(), item.getValue());
                    log.debug("解析payload项: code={}, name={}, value={}", 
                        item.getCode(), item.getName(), item.getValue());
                }
            }

            log.info("成功解析payload，共{}个数据项", payloadMap.size());
            return payloadMap;

        } catch (Exception e) {
            log.error("解析payload JSON失败: {}", payloadJson, e);
            return Collections.emptyMap();
        }
    }
}
