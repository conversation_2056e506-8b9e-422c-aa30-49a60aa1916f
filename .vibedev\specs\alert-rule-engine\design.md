# 预警规则判断系统设计文档

## 概述

本设计文档描述了预警规则判断系统的技术架构和实现方案。该系统将在现有的AlertMessageServiceImpl.send()方法中集成智能规则判断逻辑，通过资方匹配验证和条件规则评估，确保只有符合配置规则的预警事件才会被发送。

## 架构设计

### 系统架构图

```mermaid
graph TB
    A[AlertEventController.report] --> B[AlertEventService.report]
    B --> C[AlertMessageService.send]
    C --> D[RuleEngineService.evaluateRules]
    D --> E[PayloadParser]
    D --> F[CapitalMatcher]
    D --> G[ConditionEvaluator]
    D --> H[RuleProcessor]
    
    E --> I[PayloadItem解析]
    F --> J[资方匹配验证]
    G --> K[条件规则评估]
    H --> L[多规则处理]
    
    D --> M{规则匹配结果}
    M -->|匹配成功| N[发送预警消息]
    M -->|匹配失败| O[跳过发送并记录日志]
    
    subgraph "数据层"
        P[t_capital]
        Q[t_rules]
        R[t_warn_condition]
    end
    
    F --> P
    G --> Q
    G --> R
```

### 核心组件

1. **RuleEngineService**: 规则引擎核心服务
2. **PayloadParser**: 事件数据解析器
3. **CapitalMatcher**: 资方匹配器
4. **ConditionEvaluator**: 条件评估器
5. **RuleProcessor**: 规则处理器
6. **ComparisonOperatorEnum**: 运算符枚举

## 组件和接口设计

### 1. RuleEngineService 接口

```java
public interface RuleEngineService {
    /**
     * 评估规则是否匹配
     * @param alertEventDO 预警事件
     * @param ruleConditions 规则条件列表
     * @return 规则评估结果
     */
    RuleEvaluationResult evaluateRules(AlertEventDO alertEventDO, 
                                       List<AlertMessageFullInfoVO.RuleConditionVO> ruleConditions);
}
```

### 2. PayloadParser 组件

```java
public class PayloadParser {
    /**
     * 解析payload JSON字符串为PayloadItem映射
     * @param payloadJson JSON字符串
     * @return code-value映射表
     */
    public Map<String, String> parsePayload(String payloadJson);
}
```

### 3. CapitalMatcher 组件

```java
public class CapitalMatcher {
    /**
     * 验证资方是否匹配规则
     * @param payloadMap 事件数据映射
     * @param rulesDO 规则配置
     * @return 是否匹配
     */
    public boolean matchCapital(Map<String, String> payloadMap, RulesDO rulesDO);
}
```

### 4. ConditionEvaluator 组件

```java
public class ConditionEvaluator {
    /**
     * 评估单个条件是否满足
     * @param condition 条件配置
     * @param payloadMap 事件数据映射
     * @return 是否满足条件
     */
    public boolean evaluateCondition(WarnConditionDO condition, Map<String, String> payloadMap);
}
```

### 5. ComparisonOperatorEnum 枚举

```java
public enum ComparisonOperatorEnum {
    GT("GT", "大于"),
    GTE("GTE", "大于等于"),
    EQ("EQ", "等于"),
    LT("LT", "小于"),
    LTE("LTE", "小于等于"),
    NE("NE", "不等于");
    
    /**
     * 执行比较操作
     * @param value1 值1
     * @param value2 值2
     * @return 比较结果
     */
    public boolean compare(String value1, String value2);
}
```

## 数据模型

### 1. RuleEvaluationResult 结果模型

```java
@Data
@Builder
public class RuleEvaluationResult {
    /** 是否匹配成功 */
    private boolean matched;
    
    /** 匹配的规则ID */
    private Long matchedRuleId;
    
    /** 匹配的规则名称 */
    private String matchedRuleName;
    
    /** 匹配失败原因 */
    private String failureReason;
    
    /** 处理详情 */
    private List<RuleProcessDetail> processDetails;
}
```

### 2. RuleProcessDetail 处理详情

```java
@Data
@Builder
public class RuleProcessDetail {
    /** 规则ID */
    private Long ruleId;
    
    /** 规则名称 */
    private String ruleName;
    
    /** 处理状态 */
    private ProcessStatus status;
    
    /** 处理消息 */
    private String message;
    
    /** 条件评估详情 */
    private List<ConditionEvaluationDetail> conditionDetails;
}
```

### 3. ConditionEvaluationDetail 条件评估详情

```java
@Data
@Builder
public class ConditionEvaluationDetail {
    /** 条件ID */
    private Long conditionId;
    
    /** 设置项 */
    private String settingItem;
    
    /** 运算符 */
    private String operator;
    
    /** 期望值 */
    private String expectedValue;
    
    /** 实际值 */
    private String actualValue;
    
    /** 评估结果 */
    private boolean result;
    
    /** 评估消息 */
    private String message;
}
```

## 核心算法设计

### 1. 规则评估主流程

```java
public RuleEvaluationResult evaluateRules(AlertEventDO alertEventDO, 
                                         List<AlertMessageFullInfoVO.RuleConditionVO> ruleConditions) {
    // 1. 解析事件数据
    Map<String, String> payloadMap = payloadParser.parsePayload(alertEventDO.getPayload());
    
    // 2. 遍历所有规则
    for (RuleConditionVO ruleCondition : ruleConditions) {
        // 3. 查询规则详细配置
        RulesDO rulesDO = rulesService.getById(ruleCondition.getRuleId());
        
        // 4. 检查规则状态
        if (!"0".equals(rulesDO.getRuleStatus())) {
            continue; // 跳过关闭的规则
        }
        
        // 5. 资方匹配验证
        if (!capitalMatcher.matchCapital(payloadMap, rulesDO)) {
            continue; // 资方不匹配，跳过此规则
        }
        
        // 6. 条件规则评估
        if (ruleProcessor.processRule(rulesDO, payloadMap)) {
            return RuleEvaluationResult.builder()
                .matched(true)
                .matchedRuleId(rulesDO.getId())
                .matchedRuleName(rulesDO.getRuleName())
                .build();
        }
    }
    
    return RuleEvaluationResult.builder()
        .matched(false)
        .failureReason("没有匹配的规则")
        .build();
}
```

### 2. 资方匹配算法

```java
public boolean matchCapital(Map<String, String> payloadMap, RulesDO rulesDO) {
    // 1. 从payload中获取资方名称
    String fundProviderName = payloadMap.get("fund_provider_name");
    if (StringUtils.isBlank(fundProviderName)) {
        return false;
    }
    
    // 2. 根据资方名称查询资方ID
    CapitalDO capital = capitalService.getOne(
        new LambdaQueryWrapper<CapitalDO>()
            .eq(CapitalDO::getCapitalName, fundProviderName)
            .eq(CapitalDO::getDeleted, false)
    );
    
    if (capital == null) {
        return false;
    }
    
    // 3. 检查规则的适用资方列表
    String applyInvestor = rulesDO.getApplyInvestor();
    if (StringUtils.isBlank(applyInvestor)) {
        return false;
    }
    
    // 4. 解析逗号分隔的资方ID列表
    List<String> capitalIds = Arrays.asList(applyInvestor.split(","));
    return capitalIds.contains(capital.getId().toString());
}
```

### 3. 条件评估算法

```java
public boolean processRule(RulesDO rulesDO, Map<String, String> payloadMap) {
    // 1. 查询规则的所有条件
    List<WarnConditionDO> conditions = warnConditionService.list(
        new LambdaQueryWrapper<WarnConditionDO>()
            .eq(WarnConditionDO::getRulesId, rulesDO.getId())
            .eq(WarnConditionDO::getDeleted, false)
    );
    
    if (CollectionUtils.isEmpty(conditions)) {
        return false;
    }
    
    // 2. 根据规则匹配模式进行评估
    boolean isAllMatch = "1".equals(rulesDO.getRuleMatching()); // 1-满足所有条件，0-满足任一条件
    
    for (WarnConditionDO condition : conditions) {
        boolean conditionResult = conditionEvaluator.evaluateCondition(condition, payloadMap);
        
        if (isAllMatch) {
            // 满足所有条件模式：任一条件不满足则返回false
            if (!conditionResult) {
                return false;
            }
        } else {
            // 满足任一条件模式：任一条件满足则返回true
            if (conditionResult) {
                return true;
            }
        }
    }
    
    // 满足所有条件模式：所有条件都满足
    // 满足任一条件模式：没有条件满足
    return isAllMatch;
}
```

## 错误处理策略

### 1. 异常分类

- **数据解析异常**: payload格式错误
- **数据库查询异常**: 规则配置查询失败
- **数据类型转换异常**: 数值比较时类型转换失败
- **配置异常**: 规则配置不完整或格式错误

### 2. 异常处理原则

1. **降级处理**: 规则判断异常时不影响预警消息发送
2. **详细日志**: 记录异常详情便于排查
3. **快速失败**: 关键异常快速返回避免资源浪费
4. **优雅降级**: 部分规则异常时继续处理其他规则

### 3. 异常处理实现

```java
public RuleEvaluationResult evaluateRules(AlertEventDO alertEventDO, 
                                         List<AlertMessageFullInfoVO.RuleConditionVO> ruleConditions) {
    try {
        // 核心逻辑
        return doEvaluateRules(alertEventDO, ruleConditions);
    } catch (PayloadParseException e) {
        log.error("事件数据解析失败，事件ID：{}，错误：{}", alertEventDO.getEventId(), e.getMessage(), e);
        return RuleEvaluationResult.builder()
            .matched(false)
            .failureReason("事件数据解析失败")
            .build();
    } catch (DatabaseException e) {
        log.error("规则配置查询失败，事件ID：{}，错误：{}", alertEventDO.getEventId(), e.getMessage(), e);
        // 降级处理：数据库异常时允许发送预警
        return RuleEvaluationResult.builder()
            .matched(true)
            .failureReason("规则配置查询失败，降级处理")
            .build();
    } catch (Exception e) {
        log.error("规则评估异常，事件ID：{}，错误：{}", alertEventDO.getEventId(), e.getMessage(), e);
        return RuleEvaluationResult.builder()
            .matched(false)
            .failureReason("规则评估异常")
            .build();
    }
}
```

## 测试策略

### 1. 单元测试

- **PayloadParser测试**: 测试各种payload格式的解析
- **CapitalMatcher测试**: 测试资方匹配逻辑
- **ConditionEvaluator测试**: 测试各种运算符的条件评估
- **ComparisonOperatorEnum测试**: 测试运算符比较逻辑

### 2. 集成测试

- **RuleEngineService测试**: 测试完整的规则评估流程
- **数据库集成测试**: 测试与数据库的交互
- **异常场景测试**: 测试各种异常情况的处理

### 3. 性能测试

- **规则评估性能**: 测试大量规则时的评估性能
- **数据库查询性能**: 测试规则配置查询的性能
- **内存使用测试**: 测试大量数据时的内存使用情况

## 部署和监控

### 1. 配置管理

- **规则引擎开关**: 支持动态开启/关闭规则引擎
- **性能参数配置**: 超时时间、批量查询大小等
- **日志级别配置**: 支持动态调整日志级别

### 2. 监控指标

- **规则评估成功率**: 监控规则评估的成功率
- **规则匹配率**: 监控规则匹配的比例
- **性能指标**: 评估耗时、数据库查询耗时
- **异常指标**: 各类异常的发生频率

### 3. 告警机制

- **性能告警**: 评估耗时超过阈值时告警
- **异常告警**: 异常率超过阈值时告警
- **数据质量告警**: 数据解析失败率过高时告警

## 实现计划

### 阶段1: 基础组件开发
1. 创建ComparisonOperatorEnum枚举类
2. 实现PayloadParser组件
3. 创建基础数据模型类

### 阶段2: 核心服务开发
1. 实现RuleEngineService接口和实现类
2. 实现CapitalMatcher组件
3. 实现ConditionEvaluator组件
4. 实现RuleProcessor组件

### 阶段3: 集成和测试
1. 在AlertMessageServiceImpl中集成规则引擎
2. 编写单元测试和集成测试
3. 性能测试和优化

### 阶段4: 监控和部署
1. 添加监控指标和日志
2. 配置管理和部署
3. 文档完善和培训

## 技术决策说明

### 1. 为什么选择在send方法中集成规则判断？
- **最小侵入性**: 不改变现有的事件上报流程
- **统一入口**: 所有预警消息都经过统一的规则判断
- **易于维护**: 规则逻辑集中在一个地方

### 2. 为什么使用Map<String, String>存储payload数据？
- **简化处理**: 避免复杂的JSON对象解析
- **统一格式**: 所有数据都转换为字符串便于比较
- **性能考虑**: 减少对象创建和内存占用

### 3. 为什么采用枚举实现运算符？
- **类型安全**: 编译时检查运算符的有效性
- **扩展性**: 易于添加新的运算符类型
- **可读性**: 代码更加清晰易懂

### 4. 为什么选择降级处理策略？
- **系统稳定性**: 确保预警功能的可用性
- **用户体验**: 避免因规则异常导致预警丢失
- **可观测性**: 通过日志记录异常便于排查

## 风险评估和缓解措施

### 1. 性能风险
**风险**: 规则数量增多时可能影响预警发送性能
**缓解措施**:
- 实现规则缓存机制
- 优化数据库查询
- 设置评估超时时间

### 2. 数据一致性风险
**风险**: 规则配置变更时可能导致判断结果不一致
**缓解措施**:
- 实现规则版本管理
- 添加配置变更审计日志
- 提供规则测试工具

### 3. 扩展性风险
**风险**: 未来需求变化时可能需要大幅修改
**缓解措施**:
- 采用插件化架构设计
- 预留扩展接口
- 完善的文档和测试

## 总结

本设计文档详细描述了预警规则判断系统的技术架构、组件设计、核心算法、错误处理、测试策略和部署监控方案。通过模块化的设计和完善的异常处理机制，确保系统的稳定性和可扩展性。同时，通过详细的实现计划和风险评估，为项目的顺利实施提供了保障。
