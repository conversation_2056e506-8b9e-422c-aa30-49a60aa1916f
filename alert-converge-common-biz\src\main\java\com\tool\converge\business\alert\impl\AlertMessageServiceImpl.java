package com.tool.converge.business.alert.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.converge.business.alert.AlertMessageService;
import com.tool.converge.business.alert.AlertModelConfigService;
import com.tool.converge.business.alert.AlertModelNotifiersService;
import com.tool.converge.business.alert.AlertModelRuleService;
import com.tool.converge.business.alert.AlertModelService;
import com.tool.converge.business.rules.RulesService;
import com.tool.converge.business.system.SysDictValueService;
import com.tool.converge.business.alert.rule.RuleEngineService;
import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;

import com.tool.converge.common.enums.AlertMessageWarnTypeEnum;
import com.tool.converge.repository.domain.alert.bo.AlertMessageSaveBO;

import com.tool.converge.repository.domain.alert.bo.AlertMessageQueryParamsBO;
import com.tool.converge.repository.domain.alert.bo.AlertMessageExportBO;
import com.tool.converge.repository.domain.alert.db.*;
import com.tool.converge.repository.domain.alert.vo.AlertMessageDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageFullInfoVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageFullInfoCompositeVO;
import com.tool.converge.repository.domain.alert.vo.AlertMessagePageVO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import com.tool.converge.repository.domain.system.db.SysDictValueDO;
import com.tool.converge.repository.domain.alert.vo.AlertMessageExportVO;
import com.tool.converge.common.utils.ExcelUtils;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.alert.db.AlertMessageDO;
import com.tool.converge.repository.domain.alert.db.AlertModelDO;
import com.tool.converge.repository.mapper.alert.AlertMessageMapper;
import com.tool.converge.common.constant.KeyConstant;
import com.hzed.structure.redis.util.RedisUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警消息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
public class AlertMessageServiceImpl extends ServiceImpl<AlertMessageMapper, AlertMessageDO> implements AlertMessageService {

    @Resource
    private AlertMessageMapper alertMessageMapper;

    @Resource
    private SysDictValueService sysDictValueService;

    @Resource
    @Lazy
    private AlertModelService alertModelService;

    @Resource
    private AlertModelConfigService alertModelConfigService;

    @Resource
    private AlertModelRuleService alertModelRuleService;

    @Resource
    private AlertModelNotifiersService alertModelNotifiersService;

    @Resource
    private RulesService rulesService;

    @Resource
    private com.tool.converge.business.dingtalk.service.DingTalkMessageService dingTalkMessageService;

    @Resource
    private RuleEngineService ruleEngineService;


    @Override
    public Boolean saveInfo(AlertMessageSaveBO saveBO) {
        AlertMessageDO entity = new AlertMessageDO();
        BeanUtils.copyProperties(saveBO, entity);
        return save(entity);
    }

    @Override
    public AlertMessageDetailVO getInfo(Long id) {
        if (id == null) {
            throw new ServiceException("ID不能为空");
        }
        AlertMessageDO entity = getById(id);
        return AlertMessageDetailVO.of(entity);
    }

    @Override
    public IPage<AlertMessagePageVO> getPageInfo(AlertMessageQueryParamsBO queryParamsBO) {
        IPage<AlertMessageDO> page = new Page<>(queryParamsBO.getPageNum(), queryParamsBO.getPageSize());
        
        // 执行分页查询
        IPage<AlertMessageDO> resultPage = alertMessageMapper.selectMessagePage(page, queryParamsBO);
        
        // 转换为VO
        List<AlertMessagePageVO> voList = resultPage.getRecords().stream()
                .map(AlertMessagePageVO::of)
                .collect(Collectors.toList());
        
        IPage<AlertMessagePageVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public void export(AlertMessageExportBO alertMessageExportBO, HttpServletResponse httpServletResponse) {
        // 根据ID列表查询预警消息数据
        List<AlertMessageDO> alertMessageList = listByIds(alertMessageExportBO.getIds());

        if (alertMessageList == null || alertMessageList.isEmpty()) {
            throw new ServiceException("没有找到要导出的数据");
        }

        Map<String,String> businessTypeMap = sysDictValueService.listByKeyName("business_type").stream()
                .collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        Map<String,String> alertTypeMap = sysDictValueService.listByKeyName("alert_type").stream()
                .collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        Map<String,String> warnLevelMap = sysDictValueService.listByKeyName("warn_level").stream()
                .collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));

        // 转换为导出VO
        List<AlertMessageExportVO> exportVOList = alertMessageList.stream()
                .map(AlertMessageExportVO::of)
                .map(vo -> {
                    vo.setBusinessType(businessTypeMap.get(vo.getBusinessType())!=null?businessTypeMap.get(vo.getBusinessType()):vo.getBusinessType());
                    vo.setAlertType(alertTypeMap.get(vo.getAlertType())!=null?alertTypeMap.get(vo.getAlertType()):vo.getAlertType());
                    vo.setWarnLevel(warnLevelMap.get(vo.getWarnLevel())!=null?warnLevelMap.get(vo.getWarnLevel()):vo.getWarnLevel());
                    return vo;
                })
                .collect(Collectors.toList());

        // 生成文件名：预警消息_yyyyMMddHHmmss
        String fileName = "预警消息_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        // 导出Excel
        ExcelUtils.writeExcel(exportVOList, AlertMessageExportVO.class, alertMessageExportBO.getOrderByColumns(), fileName, httpServletResponse);
    }

    @Override
    public void send(AlertEventDO alertEventDO) {
        //查出事件对应的预警模型
        AlertModelDO alertModelDO = alertModelService.list(
            new LambdaQueryWrapper<AlertModelDO>()
            .eq(AlertModelDO::getModelCode, alertEventDO.getModelCode())
            .eq(AlertModelDO::getDeleted, false)
            ).stream().findFirst().orElse(null);
        if (alertModelDO == null) {
            return;
        }
        //---根据预警模板来判断是否需要发送预警消息---
        //如果不没有配置规则并且没有开启发送消息则无需处理
        if (!alertModelDO.getWarned() || !alertModelDO.getRelated()) {
            return;
        }
        
        // 获取完整的预警信息
        AlertMessageFullInfoVO fullInfo = buildAlertMessageFullInfo(alertEventDO, alertModelDO);

        // 频率控制处理
        if (frequencyControl(alertEventDO,alertModelDO,fullInfo)){
            return;
        }

        // 规则判断，看看是否命中规则
        if (!evaluateRules(alertEventDO, fullInfo)) {
            return;
        }

        // 实现预警消息发送逻辑
        try {
            // 1. 根据预警方式发送消息
            sendAlertNotification(fullInfo);

            // 2. 将预警消息存入数据库
            saveAlertMessage(alertEventDO, alertModelDO, fullInfo);

            log.info("预警消息发送成功，事件ID：{}，预警名称：{}",
                    alertEventDO.getEventId(), alertModelDO.getModelName());

        } catch (Exception e) {
            log.error("预警消息发送失败，事件ID：{}，预警名称：{}",
                    alertEventDO.getEventId(), alertModelDO.getModelName(), e);
            throw new ServiceException(String.format("预警消息发送失败，事件ID：%s，预警名称：%s", alertEventDO.getEventId(), alertModelDO.getModelName()));
        }

    }

    /**
     * 频率控制
     *
     * @param alertEventDO  预警事件
     * @param alertModelDO  预警模型
     * @param fullInfo  预警信息
     * @return  是否发送
     */
    private boolean frequencyControl(AlertEventDO alertEventDO,AlertModelDO alertModelDO,AlertMessageFullInfoVO fullInfo) {
        if (fullInfo.getFrequency() != null && fullInfo.getFrequency() > 0) {
            // 生成频率控制的唯一标识：业务唯一编码 + 期次 + 原因 + 预警名称
            String frequencyKey = generateFrequencyKey(alertEventDO.getServiceNo(),
                    alertEventDO.getPeriod(),
                    alertEventDO.getReason(),
                    alertModelDO.getModelName());

            // 检查当前小时内的发送次数
            String redisKey = KeyConstant.ALERT_MESSAGE_FREQUENCY + frequencyKey;
            String countStr = RedisUtil.get(redisKey);
            Integer currentCount = 0;
            if (countStr != null) {
                try {
                    currentCount = Integer.parseInt(countStr);
                } catch (NumberFormatException e) {
                    log.error("Redis中频率控制计数值格式错误，重置为0。Key：{}，Value：{}", redisKey, countStr);
                    throw new ServiceException(String.format("Redis中频率控制计数值格式错误，重置为0。Key：%s，Value：%s", redisKey, countStr));
                }
            }

            // 如果当前次数已达到限制，则不发送消息
            if (currentCount >= fullInfo.getFrequency()) {
                log.info("预警消息发送频率已达限制，跳过发送。事件id：{}，频率限制：{}，当前次数：{}，频率Key：{}",
                        alertEventDO.getEventId(), fullInfo.getFrequency(), currentCount, frequencyKey);
                return false;
            }

            // 增加发送次数计数
            if (countStr == null) {
                // 第一次设置，同时设置过期时间（1小时）
                RedisUtil.set(redisKey, "1");
                RedisUtil.expire(redisKey, KeyConstant.THREE_HUNDRED * 12, TimeUnit.SECONDS);
            } else {
                // 已存在key，只更新计数，不改变过期时间
                RedisUtil.set(redisKey, String.valueOf(currentCount + 1));
            }
            log.info("预警消息频率控制计数，事件id：{}，当前次数：{}，频率限制：{}，频率Key：{}",
                    alertEventDO.getEventId(), currentCount + 1, fullInfo.getFrequency(), frequencyKey);
        }
        return true;
    }

    /**
     * 生成频率控制的唯一标识
     *
     * @param serviceNo 业务唯一编码
     * @param period 期次
     * @param reason 原因
     * @param modelName 预警名称
     * @return MD5哈希值
     */
    private String generateFrequencyKey(String serviceNo, String period, String reason, String modelName) {
        // 拼接所有字段，用分隔符连接避免冲突
        String combined = String.join("|",
                                    serviceNo != null ? serviceNo : "",
                                    period != null ? period : "",
                                    reason != null ? reason : "",
                                    modelName != null ? modelName : "");

        // 生成MD5哈希值作为唯一标识
        return SecureUtil.md5(combined);
    }
    
    /**
     * 构建完整的预警消息信息
     *
     * @param alertEventDO 预警事件
     * @param alertModelDO 预警模型
     * @return 完整的预警消息信息
     */    private AlertMessageFullInfoVO buildAlertMessageFullInfo(AlertEventDO alertEventDO, AlertModelDO alertModelDO) {
        AlertMessageFullInfoVO.AlertMessageFullInfoVOBuilder builder = AlertMessageFullInfoVO.builder();
        // 基本信息
        builder.platformName(alertEventDO.getPlatformName())
               .period(alertEventDO.getPeriod())
               .modelName(alertModelDO.getModelName())
               .businessType(alertModelDO.getBusinessType())
               .alertType(alertModelDO.getAlertType())
               .reason(alertEventDO.getReason())
               .createTime(alertEventDO.getCreateTime())
               .related(alertModelDO.getRelated())
               .warned(alertModelDO.getWarned());
        // 通过关联查询一次性获取所有需要的数据
        AlertMessageFullInfoCompositeVO compositeVO = alertModelConfigService.getFullInfoByModelId(alertModelDO.getId());
        if (compositeVO != null && compositeVO.getAlertModelConfig() != null) {
            AlertModelConfigDO alertModelConfigDO = compositeVO.getAlertModelConfig();
            builder.frequency(alertModelConfigDO.getFrequency())
                   .warnType(alertModelConfigDO.getWarnType())
                   .webhook(alertModelConfigDO.getWebhook())
                   .warnContent(alertModelConfigDO.getWarnContent());
            // 获取通知人员信息
            List<AlertModelNotifiersDO> notifiers = compositeVO.getNotifiers();
            // 获取用户详细信息(姓名、钉钉ID、手机号)
            if (notifiers != null && !notifiers.isEmpty()) {
                List<com.tool.converge.repository.domain.system.db.DingTalkUserDO> users = compositeVO.getUsers();
                if (users != null && !users.isEmpty()) {
                    Map<Long, com.tool.converge.repository.domain.system.db.DingTalkUserDO> userMap = users.stream()
                        .collect(Collectors.toMap(com.tool.converge.repository.domain.system.db.DingTalkUserDO::getId, user -> user));
                    List<AlertMessageFullInfoVO.NotifierInfoVO> notifierInfoVos = notifiers.stream()
                        .map(notifier -> {
                            com.tool.converge.repository.domain.system.db.DingTalkUserDO user = userMap.get(notifier.getUserId());
                            if (user != null) {
                                return AlertMessageFullInfoVO.NotifierInfoVO.builder()
                                    .userId(user.getId())
                                    .nickName(user.getName())
                                    .dingtalkUserId(user.getUserId())
                                    .phonenumber(user.getMobile())
                                    .build();
                            } else {
                                return AlertMessageFullInfoVO.NotifierInfoVO.builder()
                                    .userId(notifier.getUserId())
                                    .build();
                            }
                        })
                        .collect(Collectors.toList());
                    builder.notifiers(notifierInfoVos);
                }
            }
            // 获取关联的规则信息
            List<AlertModelRuleDO> modelRules = compositeVO.getModelRules();
            if (modelRules != null && !modelRules.isEmpty()) {
                List<RulesDO> rules = compositeVO.getRules();
                if (rules != null && !rules.isEmpty()) {
                    List<AlertMessageFullInfoVO.RuleConditionVO> ruleConditionVos = rules.stream()
                        .map(rule -> AlertMessageFullInfoVO.RuleConditionVO.builder()
                            .ruleId(rule.getId())
                            .ruleName(rule.getRuleName())
                            .ruleCode(rule.getRuleCode())
                            .build())
                        .collect(Collectors.toList());
                    
                    builder.ruleConditions(ruleConditionVos);
                }
            }
        }
        return builder.build();
    }

    /**
     * 发送预警通知
     *
     * @param fullInfo 完整的预警信息
     */
    private void sendAlertNotification(AlertMessageFullInfoVO fullInfo) {
        if (fullInfo.getWarnType() == null) {
            log.warn("预警方式为空，跳过消息发送");
            return;
        }

        // 发送钉钉消息 (预警方式：1-钉钉，3-钉钉+短信)
        if (fullInfo.getWarnType() == AlertMessageWarnTypeEnum.DING_TALK.getCode() || fullInfo.getWarnType() == AlertMessageWarnTypeEnum.DING_TALK_AND_SMS.getCode()) {
            dingTalkMessageService.sendAlertMessage(fullInfo);
        }

        // 发送短信消息(预警方式：2-短信，3-钉钉+短信)
        if (fullInfo.getWarnType() == AlertMessageWarnTypeEnum.SMS.getCode() || fullInfo.getWarnType() == AlertMessageWarnTypeEnum.DING_TALK_AND_SMS.getCode()) {

        }
    }

    /**
     * 保存预警消息到数据库
     *
     * @param alertEventDO 预警事件
     * @param alertModelDO 预警模型
     * @param fullInfo 完整的预警信息
     */
    private void saveAlertMessage(AlertEventDO alertEventDO, AlertModelDO alertModelDO, AlertMessageFullInfoVO fullInfo) {
        try {
            AlertMessageDO alertMessage = AlertMessageDO.builder()
                    .eventId(alertEventDO.getEventId())
                    .modelCode(alertModelDO.getModelCode())
                    .modelName(alertModelDO.getModelName())
                    .alertType(alertModelDO.getAlertType())
                    .businessType(alertModelDO.getBusinessType())
                    .platformName(alertEventDO.getPlatformName())
                    .period(alertEventDO.getPeriod())
                    .reason(alertEventDO.getReason())
                    .serviceNo(alertEventDO.getServiceNo())
                    .indexValue(alertEventDO.getIndexValue())
                    .payload(alertEventDO.getPayload())
                    .state(alertEventDO.getState())
                    .related(alertModelDO.getRelated())
                    .alertTime(alertEventDO.getCreateTime())
                    .warnType(fullInfo.getWarnType())
                    .frequency(fullInfo.getFrequency())
                    .build();

            // 保存到数据库
            this.save(alertMessage);

            log.info("预警消息已保存到数据库，消息ID：{}", alertMessage.getId());

        } catch (Exception e) {
            log.error("保存预警消息到数据库失败", e);
            throw e;
        }
    }

    /**
     * 规则判断处理
     *
     * @param alertEventDO 预警事件
     * @param fullInfo 完整的预警信息
     * @return 是否应该发送预警消息，true表示应该发送，false表示跳过发送
     */
    private boolean evaluateRules(AlertEventDO alertEventDO, AlertMessageFullInfoVO fullInfo) {
        long startTime = System.currentTimeMillis();

        try {
            // 检查是否有规则需要评估
            if (fullInfo.getRuleConditions() == null || fullInfo.getRuleConditions().isEmpty()) {
                log.info("预警事件无关联规则，不发生预警，事件ID：{}，预警模型：{}",
                    alertEventDO.getEventId(), alertEventDO.getModelCode());
                // 无规则时不发送预警
                return false;
            }

            // 记录规则判断开始信息
            log.info("开始规则判断，事件ID：{}，预警模型：{}，规则数量：{}，业务编码：{}，期次：{}",
                alertEventDO.getEventId(), alertEventDO.getModelCode(),
                fullInfo.getRuleConditions().size(), alertEventDO.getServiceNo(), alertEventDO.getPeriod());

            // 记录关联的规则信息
            if (log.isDebugEnabled()) {
                fullInfo.getRuleConditions().forEach(rule ->
                    log.debug("关联规则信息，事件ID：{}，规则ID：{}，规则名称：{}，规则编码：{}",
                        alertEventDO.getEventId(), rule.getRuleId(), rule.getRuleName(), rule.getRuleCode()));
            }

            // 提取规则ID列表
            List<Long> ruleIds = fullInfo.getRuleConditions().stream()
                .map(AlertMessageFullInfoVO.RuleConditionVO::getRuleId)
                .collect(Collectors.toList());

            // 调用规则引擎进行评估
            RuleEvaluationResult result = ruleEngineService.evaluateRules(alertEventDO, ruleIds);

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            if (result.isMatched()) {
                // 记录匹配成功的详细信息
                log.info("规则匹配成功，将发送预警消息，事件ID：{}，匹配规则：{}（ID：{}），规则编码：{}，预警级别：{}，评估耗时：{}ms",
                    alertEventDO.getEventId(), result.getMatchedRuleName(), result.getMatchedRuleId(),
                    result.getMatchedRuleCode(), result.getWarnLevel(), duration);

                // 记录处理详情
                logRuleProcessDetails(alertEventDO.getEventId(), result);

                return true;
            } else {
                // 记录匹配失败的详细信息
                log.info("规则匹配失败，跳过预警消息发送，事件ID：{}，失败原因：{}，评估耗时：{}ms",
                    alertEventDO.getEventId(), result.getFailureReason(), duration);

                // 记录失败的处理详情
                logRuleProcessDetails(alertEventDO.getEventId(), result);

                return false;
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.error("规则判断异常，事件ID：{}，预警模型：{}，错误：{}，评估耗时：{}ms，采用降级策略继续发送预警",
                alertEventDO.getEventId(), alertEventDO.getModelCode(), e.getMessage(), duration, e);
            // 降级处理：规则判断异常时继续发送预警，确保预警功能可用性
            return true;
        }
    }

    /**
     * 记录规则处理详情
     *
     * @param eventId 事件ID
     * @param result 规则评估结果
     */
    private void logRuleProcessDetails(String eventId, RuleEvaluationResult result) {
        if (result.getProcessDetails() == null || result.getProcessDetails().isEmpty()) {
            return;
        }

        // 记录每个规则的处理详情
        result.getProcessDetails().forEach(detail -> {
            if (detail.getStatus() != null) {
                switch (detail.getStatus()) {
                    case SUCCESS:
                        log.info("规则处理成功，事件ID：{}，规则ID：{}，规则名称：{}，处理消息：{}",
                            eventId, detail.getRuleId(), detail.getRuleName(), detail.getMessage());

                        // 记录条件评估详情
                        if (detail.getConditionDetails() != null && !detail.getConditionDetails().isEmpty()) {
                            detail.getConditionDetails().forEach(conditionDetail ->
                                log.debug("条件评估详情，事件ID：{}，规则ID：{}，条件ID：{}，设置项：{}，运算符：{}，期望值：{}，实际值：{}，评估结果：{}，消息：{}",
                                    eventId, detail.getRuleId(), conditionDetail.getConditionId(),
                                    conditionDetail.getSettingItem(), conditionDetail.getOperator(),
                                    conditionDetail.getExpectedValue(), conditionDetail.getActualValue(),
                                    conditionDetail.isResult(), conditionDetail.getMessage()));
                        }
                        break;

                    case FAILED:
                        log.warn("规则处理失败，事件ID：{}，规则ID：{}，规则名称：{}，失败原因：{}",
                            eventId, detail.getRuleId(), detail.getRuleName(), detail.getMessage());
                        break;

                    case SKIPPED:
                        log.debug("规则处理跳过，事件ID：{}，规则ID：{}，规则名称：{}，跳过原因：{}",
                            eventId, detail.getRuleId(), detail.getRuleName(), detail.getMessage());
                        break;

                    case ERROR:
                        log.error("规则处理异常，事件ID：{}，规则ID：{}，规则名称：{}，异常信息：{}",
                            eventId, detail.getRuleId(), detail.getRuleName(), detail.getMessage());
                        break;

                    default:
                        log.debug("规则处理状态未知，事件ID：{}，规则ID：{}，规则名称：{}，状态：{}，消息：{}",
                            eventId, detail.getRuleId(), detail.getRuleName(), detail.getStatus(), detail.getMessage());
                        break;
                }
            }
        });

        // 记录汇总信息
        int totalRules = result.getProcessDetails().size();
        int successCount = result.getMatchedCount();
        int failedCount = result.getFailedCount();
        int skippedCount = result.getSkippedCount();

        log.info("规则处理汇总，事件ID：{}，总规则数：{}，成功：{}，失败：{}，跳过：{}",
            eventId, totalRules, successCount, failedCount, skippedCount);
    }

}
