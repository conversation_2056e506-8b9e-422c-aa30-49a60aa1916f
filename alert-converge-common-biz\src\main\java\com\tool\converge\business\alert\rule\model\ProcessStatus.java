package com.tool.converge.business.alert.rule.model;

/**
 * 处理状态枚举
 * 用于表示规则处理过程中的各种状态
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
public enum ProcessStatus {
    
    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),
    
    /**
     * 失败
     */
    FAILED("FAILED", "失败"),
    
    /**
     * 跳过
     */
    SKIPPED("SKIPPED", "跳过"),
    
    /**
     * 错误
     */
    ERROR("ERROR", "错误"),
    
    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中");
    
    private final String code;
    private final String description;
    
    ProcessStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态
     * 
     * @param code 状态代码
     * @return 处理状态
     */
    public static ProcessStatus fromCode(String code) {
        for (ProcessStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的处理状态代码: " + code);
    }
    
    /**
     * 检查是否为成功状态
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 检查是否为失败状态
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return this == FAILED || this == ERROR;
    }
    
    /**
     * 检查是否为跳过状态
     * 
     * @return 是否跳过
     */
    public boolean isSkipped() {
        return this == SKIPPED;
    }
}
