package com.tool.converge.business.alert.rule.exception;

/**
 * 规则引擎异常
 * 用于封装规则引擎处理过程中的各种异常
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
public class RuleEngineException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 异常类型
     */
    private final ExceptionType type;

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 异常类型枚举
     */
    public enum ExceptionType {
        /**
         * 数据解析异常
         */
        PARSE_ERROR("PARSE_ERROR", "数据解析异常"),
        
        /**
         * 数据库查询异常
         */
        DATABASE_ERROR("DATABASE_ERROR", "数据库查询异常"),
        
        /**
         * 规则配置异常
         */
        RULE_CONFIG_ERROR("RULE_CONFIG_ERROR", "规则配置异常"),
        
        /**
         * 条件评估异常
         */
        CONDITION_EVAL_ERROR("CONDITION_EVAL_ERROR", "条件评估异常"),
        
        /**
         * 资方匹配异常
         */
        CAPITAL_MATCH_ERROR("CAPITAL_MATCH_ERROR", "资方匹配异常"),
        
        /**
         * 系统异常
         */
        SYSTEM_ERROR("SYSTEM_ERROR", "系统异常"),
        
        /**
         * 参数验证异常
         */
        VALIDATION_ERROR("VALIDATION_ERROR", "参数验证异常");

        private final String code;
        private final String description;

        ExceptionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    public RuleEngineException(ExceptionType type, String message) {
        super(message);
        this.type = type;
        this.errorCode = type.getCode();
    }

    public RuleEngineException(ExceptionType type, String message, Throwable cause) {
        super(message, cause);
        this.type = type;
        this.errorCode = type.getCode();
    }

    public RuleEngineException(String errorCode, String message) {
        super(message);
        this.type = ExceptionType.SYSTEM_ERROR;
        this.errorCode = errorCode;
    }

    public RuleEngineException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.type = ExceptionType.SYSTEM_ERROR;
        this.errorCode = errorCode;
    }

    public ExceptionType getType() {
        return type;
    }

    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 创建数据解析异常
     */
    public static RuleEngineException parseError(String message) {
        return new RuleEngineException(ExceptionType.PARSE_ERROR, message);
    }

    /**
     * 创建数据解析异常
     */
    public static RuleEngineException parseError(String message, Throwable cause) {
        return new RuleEngineException(ExceptionType.PARSE_ERROR, message, cause);
    }

    /**
     * 创建数据库查询异常
     */
    public static RuleEngineException databaseError(String message) {
        return new RuleEngineException(ExceptionType.DATABASE_ERROR, message);
    }

    /**
     * 创建数据库查询异常
     */
    public static RuleEngineException databaseError(String message, Throwable cause) {
        return new RuleEngineException(ExceptionType.DATABASE_ERROR, message, cause);
    }

    /**
     * 创建规则配置异常
     */
    public static RuleEngineException ruleConfigError(String message) {
        return new RuleEngineException(ExceptionType.RULE_CONFIG_ERROR, message);
    }

    /**
     * 创建条件评估异常
     */
    public static RuleEngineException conditionEvalError(String message) {
        return new RuleEngineException(ExceptionType.CONDITION_EVAL_ERROR, message);
    }

    /**
     * 创建条件评估异常
     */
    public static RuleEngineException conditionEvalError(String message, Throwable cause) {
        return new RuleEngineException(ExceptionType.CONDITION_EVAL_ERROR, message, cause);
    }

    /**
     * 创建资方匹配异常
     */
    public static RuleEngineException capitalMatchError(String message) {
        return new RuleEngineException(ExceptionType.CAPITAL_MATCH_ERROR, message);
    }

    /**
     * 创建资方匹配异常
     */
    public static RuleEngineException capitalMatchError(String message, Throwable cause) {
        return new RuleEngineException(ExceptionType.CAPITAL_MATCH_ERROR, message, cause);
    }

    /**
     * 创建系统异常
     */
    public static RuleEngineException systemError(String message) {
        return new RuleEngineException(ExceptionType.SYSTEM_ERROR, message);
    }

    /**
     * 创建系统异常
     */
    public static RuleEngineException systemError(String message, Throwable cause) {
        return new RuleEngineException(ExceptionType.SYSTEM_ERROR, message, cause);
    }

    /**
     * 创建参数验证异常
     */
    public static RuleEngineException validationError(String message) {
        return new RuleEngineException(ExceptionType.VALIDATION_ERROR, message);
    }
}
