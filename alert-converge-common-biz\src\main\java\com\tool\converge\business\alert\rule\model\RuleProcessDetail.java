package com.tool.converge.business.alert.rule.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 规则处理详情
 * 用于记录单个规则的处理过程和结果
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleProcessDetail {

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 处理状态
     */
    private ProcessStatus status;

    /**
     * 处理消息
     */
    private String message;

    /**
     * 条件评估详情列表
     */
    @Builder.Default
    private List<ConditionEvaluationDetail> conditionDetails = new ArrayList<>();

    /**
     * 处理开始时间（毫秒时间戳）
     */
    private Long processStartTime;

    /**
     * 处理结束时间（毫秒时间戳）
     */
    private Long processEndTime;

    /**
     * 规则匹配模式（0-满足任一条件，1-满足所有条件）
     */
    private String ruleMatching;

    /**
     * 匹配的条件数量
     */
    private int matchedConditionCount;

    /**
     * 总条件数量
     */
    private int totalConditionCount;

    /**
     * 资方匹配结果
     */
    private boolean capitalMatched;

    /**
     * 资方匹配消息
     */
    private String capitalMatchMessage;

    /**
     * 错误信息（如果处理过程中发生错误）
     */
    private String errorMessage;

    /**
     * 获取处理耗时（毫秒）
     * 
     * @return 处理耗时
     */
    public Long getProcessDuration() {
        if (processStartTime != null && processEndTime != null) {
            return processEndTime - processStartTime;
        }
        return null;
    }

    /**
     * 添加条件评估详情
     * 
     * @param detail 条件评估详情
     */
    public void addConditionDetail(ConditionEvaluationDetail detail) {
        if (this.conditionDetails == null) {
            this.conditionDetails = new ArrayList<>();
        }
        this.conditionDetails.add(detail);
    }

    /**
     * 获取成功的条件数量
     * 
     * @return 成功的条件数量
     */
    public int getSuccessConditionCount() {
        if (conditionDetails == null || conditionDetails.isEmpty()) {
            return 0;
        }
        return (int) conditionDetails.stream()
            .filter(ConditionEvaluationDetail::isResult)
            .count();
    }

    /**
     * 获取失败的条件数量
     * 
     * @return 失败的条件数量
     */
    public int getFailedConditionCount() {
        if (conditionDetails == null || conditionDetails.isEmpty()) {
            return 0;
        }
        return (int) conditionDetails.stream()
            .filter(detail -> !detail.isResult())
            .count();
    }

    /**
     * 检查是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasError() {
        return status == ProcessStatus.ERROR || 
               (errorMessage != null && !errorMessage.trim().isEmpty()) ||
               (conditionDetails != null && conditionDetails.stream().anyMatch(ConditionEvaluationDetail::hasError));
    }

    /**
     * 检查是否匹配成功
     * 
     * @return 是否匹配成功
     */
    public boolean isMatched() {
        return status == ProcessStatus.SUCCESS;
    }

    /**
     * 创建成功的处理详情
     * 
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @param ruleCode 规则编码
     * @param message 处理消息
     * @return 规则处理详情
     */
    public static RuleProcessDetail success(Long ruleId, String ruleName, String ruleCode, String message) {
        return RuleProcessDetail.builder()
            .ruleId(ruleId)
            .ruleName(ruleName)
            .ruleCode(ruleCode)
            .status(ProcessStatus.SUCCESS)
            .message(message)
            .build();
    }

    /**
     * 创建失败的处理详情
     * 
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @param ruleCode 规则编码
     * @param message 处理消息
     * @return 规则处理详情
     */
    public static RuleProcessDetail failure(Long ruleId, String ruleName, String ruleCode, String message) {
        return RuleProcessDetail.builder()
            .ruleId(ruleId)
            .ruleName(ruleName)
            .ruleCode(ruleCode)
            .status(ProcessStatus.FAILED)
            .message(message)
            .build();
    }

    /**
     * 创建跳过的处理详情
     * 
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @param ruleCode 规则编码
     * @param message 跳过原因
     * @return 规则处理详情
     */
    public static RuleProcessDetail skipped(Long ruleId, String ruleName, String ruleCode, String message) {
        return RuleProcessDetail.builder()
            .ruleId(ruleId)
            .ruleName(ruleName)
            .ruleCode(ruleCode)
            .status(ProcessStatus.SKIPPED)
            .message(message)
            .build();
    }

    /**
     * 创建错误的处理详情
     * 
     * @param ruleId 规则ID
     * @param ruleName 规则名称
     * @param ruleCode 规则编码
     * @param errorMessage 错误信息
     * @return 规则处理详情
     */
    public static RuleProcessDetail error(Long ruleId, String ruleName, String ruleCode, String errorMessage) {
        return RuleProcessDetail.builder()
            .ruleId(ruleId)
            .ruleName(ruleName)
            .ruleCode(ruleCode)
            .status(ProcessStatus.ERROR)
            .message("规则处理异常")
            .errorMessage(errorMessage)
            .build();
    }
}
