package com.tool.converge.business.alert.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.business.alert.rule.exception.RuleEngineException;
import com.tool.converge.business.alert.rule.fallback.RuleEngineFallbackHandler;
import com.tool.converge.business.alert.rule.model.ProcessStatus;
import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.business.alert.rule.model.RuleProcessDetail;
import com.tool.converge.business.rules.RulesService;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 预警规则引擎实现类
 * 用于评估预警事件是否匹配配置的规则
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class RuleEngineServiceImpl implements RuleEngineService {

    @Resource
    private RulesService rulesService;

    @Resource
    private PayloadParser payloadParser;

    @Resource
    private CapitalMatcher capitalMatcher;

    @Resource
    private RuleProcessor ruleProcessor;

    @Resource
    private RuleEngineFallbackHandler fallbackHandler;

    /**
     * 评估预警事件是否匹配指定的规则
     *
     * @param alertEventDO 预警事件
     * @param ruleIds 需要评估的规则ID列表
     * @return 规则评估结果
     */
    @Override
    public RuleEvaluationResult evaluateRules(AlertEventDO alertEventDO, List<Long> ruleIds) {
        long startTime = System.currentTimeMillis();
        try {
            // 参数验证
            if (!isValidAlertEvent(alertEventDO)) {
                throw RuleEngineException.validationError("预警事件数据不完整");
            }
            if (ruleIds == null || ruleIds.isEmpty()) {
                log.info("无需评估的规则，事件ID：{}", alertEventDO.getEventId());
                return RuleEvaluationResult.failure("无需评估的规则");
            }
            log.info("开始评估预警事件规则，事件ID：{}，规则数量：{}", alertEventDO.getEventId(), ruleIds.size());
            // 1. 解析payload数据
            Map<String, String> payloadMap;
            try {
                payloadMap = payloadParser.parsePayload(alertEventDO.getPayload());
                if (payloadMap == null || payloadMap.isEmpty()) {
                    throw RuleEngineException.parseError("payload解析失败或为空");
                }
            } catch (Exception e) {
                throw RuleEngineException.parseError("payload解析异常", e);
            }
            // 2. 根据规则ID列表查询规则详细信息
            List<RulesDO> targetRules;
            try {
                targetRules = rulesService.list(
                    new LambdaQueryWrapper<RulesDO>()
                        .in(RulesDO::getId, ruleIds)
                        .eq(RulesDO::getRuleStatus, "0")
                        .eq(RulesDO::getDeleted, false)
                );
            } catch (Exception e) {
                throw RuleEngineException.databaseError("查询指定规则异常", e);
            }
            if (CollectionUtils.isEmpty(targetRules)) {
                log.debug("未找到指定的启用规则，事件ID：{}，规则ID列表：{}", alertEventDO.getEventId(), ruleIds);
                return RuleEvaluationResult.failure("未找到指定的启用规则");
            }
            log.debug("找到{}个指定的启用规则，事件ID：{}", targetRules.size(), alertEventDO.getEventId());
            // 3. 逐个评估规则
            List<RuleProcessDetail> processDetails = new ArrayList<>();
            List<RulesDO> matchedRules = new ArrayList<>();
            for (RulesDO rule : targetRules) {
                try {
                    // 3.1 资方匹配验证
                    boolean capitalMatched = capitalMatcher.matchCapital(payloadMap, rule);
                    RuleProcessDetail detail = RuleProcessDetail.builder()
                        .ruleId(rule.getId())
                        .ruleName(rule.getRuleName())
                        .ruleCode(rule.getRuleCode())
                        .ruleMatching(rule.getRuleMatching())
                        .capitalMatched(capitalMatched)
                        .processStartTime(System.currentTimeMillis())
                        .build();
                    if (!capitalMatched) {
                        detail.setCapitalMatchMessage("资方不匹配");
                        detail.setStatus(ProcessStatus.SKIPPED);
                        detail.setMessage("资方不匹配，跳过规则评估");
                        detail.setProcessEndTime(System.currentTimeMillis());
                        processDetails.add(detail);
                        
                        log.debug("规则资方不匹配，跳过评估，规则ID：{}，规则名称：{}", rule.getId(), rule.getRuleName());
                        continue;
                    }
                    detail.setCapitalMatchMessage("资方匹配成功");
                    // 3.2 条件评估
                    RuleProcessDetail ruleDetail = ruleProcessor.processRuleWithDetail(rule, payloadMap);
                    // 合并资方匹配信息到规则处理详情
                    ruleDetail.setCapitalMatched(capitalMatched);
                    ruleDetail.setCapitalMatchMessage(detail.getCapitalMatchMessage());
                    processDetails.add(ruleDetail);
                    if (ruleDetail.isMatched()) {
                        matchedRules.add(rule);
                        log.info("规则匹配成功，规则ID：{}，规则名称：{}", rule.getId(), rule.getRuleName());
                    } else {
                        log.debug("规则匹配失败，规则ID：{}，规则名称：{}，原因：{}", 
                            rule.getId(), rule.getRuleName(), ruleDetail.getMessage());
                    }
                } catch (Exception e) {
                    log.error("规则评估异常，规则ID：{}，规则名称：{}，错误：{}", 
                        rule.getId(), rule.getRuleName(), e.getMessage(), e);
                    RuleProcessDetail errorDetail = RuleProcessDetail.error(
                        rule.getId(), rule.getRuleName(), rule.getRuleCode(), 
                        "规则评估异常：" + e.getMessage());
                    processDetails.add(errorDetail);
                }
            }
            // 4. 构建评估结果
            long endTime = System.currentTimeMillis();
            if (CollectionUtils.isNotEmpty(matchedRules)) {
                log.info("预警事件规则评估完成，匹配{}个规则，事件ID：{}，耗时：{}ms",
                    matchedRules.size(), alertEventDO.getId(), endTime - startTime);
                // 使用第一个匹配的规则创建成功结果
                RulesDO firstMatchedRule = matchedRules.get(0);
                RuleEvaluationResult result = RuleEvaluationResult.success(
                    firstMatchedRule.getId(),
                    firstMatchedRule.getRuleName(),
                    firstMatchedRule.getRuleCode(),
                    // 默认预警级别，可以根据实际需求调整
                    "HIGH"
                );
                result.setProcessDetails(processDetails);
                return result;
            } else {
                log.info("预警事件规则评估完成，无匹配规则，事件ID：{}，耗时：{}ms",
                    alertEventDO.getId(), endTime - startTime);
                RuleEvaluationResult result = RuleEvaluationResult.failure("无匹配规则");
                result.setProcessDetails(processDetails);
                return result;
            }
        } catch (Exception e) {
            log.error("预警事件规则评估异常，事件ID：{}，错误：{}", alertEventDO.getId(), e.getMessage(), e);

            // 使用降级处理器处理异常
            if (fallbackHandler.shouldFallback(e)) {
                return fallbackHandler.handleEvaluationException(alertEventDO, e);
            } else {
                // 对于不需要降级的异常（如参数验证异常），直接抛出
                if (e instanceof RuntimeException) {
                    throw (RuntimeException) e;
                } else {
                    throw new RuntimeException("规则评估异常", e);
                }
            }
        }
    }

    /**
     * 检查预警事件数据的完整性
     * 
     * @param alertEventDO 预警事件
     * @return 检查结果
     */
    @Override
    public boolean isValidAlertEvent(AlertEventDO alertEventDO) {
        if (alertEventDO == null) {
            return false;
        }

        if (alertEventDO.getId() == null) {
            return false;
        }

        if (StringUtils.isBlank(alertEventDO.getPayload())) {
            return false;
        }

        return true;
    }
}
